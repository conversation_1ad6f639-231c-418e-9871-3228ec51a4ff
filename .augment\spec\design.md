# 加密货币永续合约全自动量化系统 设计文档

## 概述

本系统是一个基于DeepSeek AI模型的加密货币永续合约全自动量化交易系统，采用微服务架构设计，前后端分离，支持欧易交易所的模拟盘和实盘交易。系统通过技术指标分析和AI决策引擎实现智能开仓和持仓管理。

### 核心技术栈
- **后端：** Node.js + Express.js + SQLite3 + CCXT + technicalindicators
- **前端：** React 18 + Ant Design 5.x + TypeScript
- **AI集成：** DeepSeek API (兼容OpenAI SDK)
- **数据库：** SQLite3 (配置存储) + 内存缓存 (实时数据)
- **通信：** RESTful API + Server-Sent Events (实时推送)

## 架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "前端层 (Frontend)"
        UI[React + Ant Design UI]
        Router[React Router]
        State[Redux Toolkit]
    end
    
    subgraph "后端层 (Backend)"
        API[Express.js API Server]
        Auth[认证中间件]
        Scheduler[任务调度器]
    end
    
    subgraph "核心服务层 (Core Services)"
        DataService[数据获取服务]
        IndicatorService[技术指标服务]
        AIService[AI决策服务]
        TradingService[交易执行服务]
        RiskService[风险管理服务]
    end
    
    subgraph "数据层 (Data Layer)"
        SQLite[(SQLite3 配置库)]
        Cache[(内存缓存)]
        Logs[(日志文件)]
    end
    
    subgraph "外部服务 (External Services)"
        OKX[欧易交易所 API]
        DeepSeek[DeepSeek AI API]
    end
    
    UI --> API
    API --> DataService
    API --> TradingService
    DataService --> OKX
    IndicatorService --> AIService
    AIService --> DeepSeek
    TradingService --> OKX
    API --> SQLite
    DataService --> Cache
```

### 模块化设计原则

1. **单一职责原则：** 每个服务模块只负责一个特定功能
2. **依赖注入：** 通过配置文件和环境变量管理依赖关系
3. **错误隔离：** 各模块独立的错误处理和恢复机制
4. **可扩展性：** 支持新增交易所、AI模型和技术指标

## 组件和接口设计

### 后端核心组件

#### 1. 数据获取服务 (DataService)
```javascript
class DataService {
  // 获取多时间周期K线数据
  async getMultiTimeframeData(symbol, timeframes = ['1m', '5m', '15m', '1h'])
  
  // 获取账户信息
  async getAccountInfo()
  
  // 获取持仓信息
  async getPositions()
  
  // 获取订单信息
  async getOrders(symbol)
}
```

#### 2. 技术指标服务 (IndicatorService)
```javascript
class IndicatorService {
  // 计算单个时间周期指标
  calculateIndicators(ohlcvData, indicators)
  
  // 计算多时间周期综合指标
  calculateMultiTimeframeIndicators(multiTimeframeData)
  
  // 格式化指标数据为AI输入格式
  formatIndicatorsForAI(indicators)
}
```

#### 3. AI决策服务 (AIService)
```javascript
class AIService {
  // 开仓决策引擎
  async getOpeningDecision(marketData, indicators, accountInfo)
  
  // 持仓决策引擎
  async getPositionDecision(position, marketData, indicators)
  
  // 解析AI响应
  parseAIResponse(response)
}
```

#### 4. 交易执行服务 (TradingService)
```javascript
class TradingService {
  // 执行开仓
  async openPosition(signal, riskParams)
  
  // 执行平仓
  async closePosition(positionId, reason)
  
  // 更新止盈止损
  async updateStopLoss(positionId, newStopLoss)
  
  // 计算仓位大小
  calculatePositionSize(signal, accountInfo, riskParams)
}
```

#### 5. 风险管理服务 (RiskService)
```javascript
class RiskService {
  // 验证开仓风险
  validateOpeningRisk(signal, accountInfo, currentPositions)
  
  // 计算杠杆调整后的止盈止损
  calculateLeveragedStopLoss(entryPrice, leverage, stopLossPercent)
  
  // 检查保证金充足性
  checkMarginRequirement(positionSize, leverage, accountBalance)
}
```

### 前端组件架构

#### 1. 页面组件层次结构
```
src/
├── components/           # 通用组件
│   ├── Layout/          # 布局组件
│   ├── Charts/          # 图表组件
│   └── Common/          # 公共组件
├── pages/               # 页面组件
│   ├── Dashboard/       # 仪表板
│   ├── Trading/         # 交易配置
│   ├── Monitor/         # 交易监控
│   ├── History/         # 交易历史
│   ├── Settings/        # 系统设置
│   └── Logs/           # 系统日志
├── services/            # API服务
├── store/              # Redux状态管理
├── hooks/              # 自定义Hooks
└── utils/              # 工具函数
```

#### 2. 状态管理设计
```javascript
// Redux Store 结构
{
  auth: {
    isAuthenticated: boolean,
    apiKeys: object
  },
  trading: {
    positions: array,
    orders: array,
    balance: object
  },
  market: {
    symbols: array,
    currentData: object
  },
  ai: {
    decisions: array,
    confidence: number
  },
  settings: {
    riskParams: object,
    tradingPairs: array
  }
}
```

### API接口设计

#### 1. 认证接口
```
POST /api/auth/setup     # 初始化API密钥
POST /api/auth/test      # 测试连接
PUT  /api/auth/toggle    # 切换模拟盘/实盘
```

#### 2. 交易接口
```
GET  /api/trading/account        # 获取账户信息
GET  /api/trading/positions      # 获取持仓
POST /api/trading/open           # 开仓
POST /api/trading/close          # 平仓
PUT  /api/trading/stop-loss      # 更新止损
```

#### 3. 市场数据接口
```
GET  /api/market/symbols         # 获取交易对列表
GET  /api/market/data/:symbol    # 获取市场数据
GET  /api/market/indicators/:symbol  # 获取技术指标
```

#### 4. AI决策接口
```
POST /api/ai/analyze             # AI分析请求
GET  /api/ai/decisions           # 获取决策历史
GET  /api/ai/confidence          # 获取当前置信度
```

#### 5. 系统接口
```
GET  /api/system/status          # 系统状态
GET  /api/system/logs            # 系统日志
POST /api/system/settings        # 更新设置
GET  /api/system/health          # 健康检查
```

## 实时数据处理和任务调度设计

### 1. 任务调度器架构
```javascript
class TaskScheduler {
  constructor() {
    this.tasks = new Map();
    this.intervals = new Map();
  }

  // 注册定时任务
  registerTask(name, handler, interval) {
    this.tasks.set(name, handler);
    const intervalId = setInterval(async () => {
      try {
        await handler();
      } catch (error) {
        logger.error(`Task ${name} failed:`, error);
      }
    }, interval);
    this.intervals.set(name, intervalId);
  }

  // 核心任务定义
  initializeTasks() {
    // 市场数据更新 - 每30秒
    this.registerTask('updateMarketData', this.updateMarketData.bind(this), 30000);

    // 技术指标计算 - 每分钟
    this.registerTask('calculateIndicators', this.calculateIndicators.bind(this), 60000);

    // AI决策分析 - 每2分钟
    this.registerTask('aiAnalysis', this.aiAnalysis.bind(this), 120000);

    // 持仓监控 - 每30秒
    this.registerTask('monitorPositions', this.monitorPositions.bind(this), 30000);

    // 风险检查 - 每10秒
    this.registerTask('riskCheck', this.riskCheck.bind(this), 10000);
  }
}
```

### 2. 数据流处理管道
```javascript
class DataPipeline {
  async processMarketData(symbol) {
    // 1. 获取原始数据
    const rawData = await this.dataService.getMultiTimeframeData(symbol);

    // 2. 数据验证和清洗
    const cleanData = this.validateAndCleanData(rawData);

    // 3. 更新缓存
    await this.cacheService.updateMarketData(symbol, cleanData);

    // 4. 计算技术指标
    const indicators = await this.indicatorService.calculateMultiTimeframeIndicators(cleanData);

    // 5. 更新指标缓存
    await this.cacheService.updateIndicators(symbol, indicators);

    // 6. 触发AI分析
    this.eventEmitter.emit('indicatorsUpdated', { symbol, indicators });

    return { data: cleanData, indicators };
  }

  validateAndCleanData(data) {
    // 数据完整性检查
    // 异常值检测和处理
    // 时间序列连续性验证
    return data;
  }
}
```

### 3. 事件驱动架构
```javascript
class EventManager extends EventEmitter {
  constructor() {
    super();
    this.setupEventHandlers();
  }

  setupEventHandlers() {
    // 市场数据更新事件
    this.on('marketDataUpdated', this.handleMarketDataUpdate.bind(this));

    // 技术指标更新事件
    this.on('indicatorsUpdated', this.handleIndicatorsUpdate.bind(this));

    // AI决策事件
    this.on('aiDecisionMade', this.handleAIDecision.bind(this));

    // 交易执行事件
    this.on('tradeExecuted', this.handleTradeExecution.bind(this));

    // 风险警告事件
    this.on('riskWarning', this.handleRiskWarning.bind(this));
  }

  async handleAIDecision(decision) {
    if (decision.confidence >= this.confidenceThreshold) {
      if (decision.action !== 'hold') {
        await this.tradingService.executeDecision(decision);
      }
    }
  }
}
```

### 4. 缓存管理策略
```javascript
class CacheManager {
  constructor() {
    this.marketDataCache = new Map();
    this.indicatorCache = new Map();
    this.maxCacheSize = 1000; // 最大缓存条目数
    this.ttl = 300000; // 5分钟TTL
  }

  set(key, value, customTTL = null) {
    const ttl = customTTL || this.ttl;
    const expiry = Date.now() + ttl;

    this.marketDataCache.set(key, {
      value,
      expiry
    });

    // 缓存大小控制
    if (this.marketDataCache.size > this.maxCacheSize) {
      this.evictOldest();
    }
  }

  get(key) {
    const item = this.marketDataCache.get(key);
    if (!item) return null;

    if (Date.now() > item.expiry) {
      this.marketDataCache.delete(key);
      return null;
    }

    return item.value;
  }

  evictOldest() {
    const oldestKey = this.marketDataCache.keys().next().value;
    this.marketDataCache.delete(oldestKey);
  }
}
```

## 数据模型设计

### SQLite3 数据库表结构

#### 1. 系统配置表 (system_config)
```sql
CREATE TABLE system_config (
  id INTEGER PRIMARY KEY,
  key TEXT UNIQUE NOT NULL,
  value TEXT NOT NULL,
  encrypted BOOLEAN DEFAULT FALSE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 交易参数表 (trading_params)
```sql
CREATE TABLE trading_params (
  id INTEGER PRIMARY KEY,
  max_leverage REAL NOT NULL DEFAULT 10,
  max_position_percent REAL NOT NULL DEFAULT 50,
  stop_loss_percent REAL NOT NULL DEFAULT 2,
  take_profit_percent REAL NOT NULL DEFAULT 4,
  confidence_threshold REAL NOT NULL DEFAULT 0.7,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. 交易对配置表 (trading_pairs)
```sql
CREATE TABLE trading_pairs (
  id INTEGER PRIMARY KEY,
  symbol TEXT NOT NULL,
  enabled BOOLEAN DEFAULT TRUE,
  min_notional REAL,
  tick_size REAL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 4. AI提示词模板表 (ai_prompts)
```sql
CREATE TABLE ai_prompts (
  id INTEGER PRIMARY KEY,
  type TEXT NOT NULL, -- 'opening' or 'position'
  template TEXT NOT NULL,
  version INTEGER DEFAULT 1,
  active BOOLEAN DEFAULT TRUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 内存数据结构 (临时缓存，不持久化)

#### 1. 市场数据临时缓存 (仅保留最近数据用于计算)
```javascript
{
  [symbol]: {
    '1m': [...recentOhlcvData], // 仅保留最近100条用于指标计算
    '5m': [...recentOhlcvData], // 仅保留最近100条用于指标计算
    '15m': [...recentOhlcvData], // 仅保留最近100条用于指标计算
    '1h': [...recentOhlcvData], // 仅保留最近100条用于指标计算
    lastUpdate: timestamp,
    maxLength: 100 // 限制缓存长度，超出自动删除最旧数据
  }
}
```

#### 2. 技术指标临时缓存 (计算结果临时存储)
```javascript
{
  [symbol]: {
    [timeframe]: {
      sma: [...recentValues], // 仅保留最近计算结果
      ema: [...recentValues],
      rsi: [...recentValues],
      macd: {...recentMacdData},
      bollinger: {...recentBollingerData},
      lastCalculated: timestamp,
      maxAge: 300000 // 5分钟后自动失效
    }
  }
}
```

**注意：** 所有缓存数据都是临时的，系统重启后清空。历史数据完全依赖CCXT实时获取，不进行任何持久化存储。

## AI决策引擎详细设计

### 1. 开仓引擎提示词模板
```
你是一个专业的加密货币量化交易分析师。请基于以下技术指标数据分析当前市场状况，并给出开仓建议。

**市场数据:**
- 交易对: {symbol}
- 当前价格: {currentPrice}
- 24小时涨跌幅: {change24h}%

**多时间周期技术指标:**
1分钟周期: {indicators_1m}
5分钟周期: {indicators_5m}
15分钟周期: {indicators_15m}
1小时周期: {indicators_1h}

**账户信息:**
- 可用资金: {availableBalance} USDT
- 当前持仓: {currentPositions}

请分析并返回JSON格式的建议:
{
  "action": "long|short|hold",
  "confidence": 0.0-1.0,
  "reasoning": "详细分析理由",
  "entry_price": "建议入场价格",
  "stop_loss": "止损价格",
  "take_profit": "止盈价格"
}
```

### 2. 持仓引擎提示词模板
```
你是一个专业的加密货币持仓管理分析师。请基于当前持仓状况和最新市场数据，给出持仓管理建议。

**当前持仓信息:**
- 交易对: {symbol}
- 持仓方向: {side}
- 持仓数量: {size}
- 开仓价格: {entryPrice}
- 当前价格: {currentPrice}
- 未实现盈亏: {unrealizedPnl}
- 持仓时间: {holdingTime}

**最新技术指标:**
{latestIndicators}

**风险参数:**
- 当前止损: {currentStopLoss}
- 当前止盈: {currentTakeProfit}
- 最大回撤: {maxDrawdown}

请分析并返回JSON格式的建议:
{
  "action": "hold|close|adjust_stop|adjust_target",
  "confidence": 0.0-1.0,
  "reasoning": "详细分析理由",
  "new_stop_loss": "新止损价格(如需调整)",
  "new_take_profit": "新止盈价格(如需调整)"
}
```

### 3. AI响应解析和验证
```javascript
class AIResponseValidator {
  validateOpeningResponse(response) {
    const required = ['action', 'confidence', 'reasoning'];
    const validActions = ['long', 'short', 'hold'];

    // 验证必需字段
    for (const field of required) {
      if (!response[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // 验证动作类型
    if (!validActions.includes(response.action)) {
      throw new Error(`Invalid action: ${response.action}`);
    }

    // 验证置信度范围
    if (response.confidence < 0 || response.confidence > 1) {
      throw new Error(`Invalid confidence: ${response.confidence}`);
    }

    return true;
  }

  validatePositionResponse(response) {
    const validActions = ['hold', 'close', 'adjust_stop', 'adjust_target'];

    if (!validActions.includes(response.action)) {
      throw new Error(`Invalid position action: ${response.action}`);
    }

    return true;
  }
}
```

### 4. 技术指标数据格式化
```javascript
class IndicatorFormatter {
  formatForAI(indicators, timeframe) {
    return {
      trend: {
        sma_20: indicators.sma20[indicators.sma20.length - 1],
        sma_50: indicators.sma50[indicators.sma50.length - 1],
        ema_12: indicators.ema12[indicators.ema12.length - 1],
        ema_26: indicators.ema26[indicators.ema26.length - 1]
      },
      momentum: {
        rsi: indicators.rsi[indicators.rsi.length - 1],
        macd: {
          macd: indicators.macd.MACD[indicators.macd.MACD.length - 1],
          signal: indicators.macd.signal[indicators.macd.signal.length - 1],
          histogram: indicators.macd.histogram[indicators.macd.histogram.length - 1]
        }
      },
      volatility: {
        bollinger: {
          upper: indicators.bollinger.upper[indicators.bollinger.upper.length - 1],
          middle: indicators.bollinger.middle[indicators.bollinger.middle.length - 1],
          lower: indicators.bollinger.lower[indicators.bollinger.lower.length - 1]
        },
        atr: indicators.atr[indicators.atr.length - 1]
      },
      volume: {
        volume_sma: indicators.volumeSMA[indicators.volumeSMA.length - 1],
        current_volume: indicators.volume[indicators.volume.length - 1]
      }
    };
  }
}
```

## 错误处理设计

### 1. 分层错误处理策略

#### API层错误处理
```javascript
// 统一错误响应格式
{
  success: false,
  error: {
    code: 'ERROR_CODE',
    message: '用户友好的错误信息',
    details: '技术详细信息',
    timestamp: '2025-01-08T14:40:00Z'
  }
}
```

#### 服务层错误处理
```javascript
class CustomError extends Error {
  constructor(code, message, details = null) {
    super(message);
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }
}
```

### 2. 错误类型定义

- **EXCHANGE_ERROR:** 交易所API错误
- **AI_ERROR:** AI服务错误
- **VALIDATION_ERROR:** 数据验证错误
- **RISK_ERROR:** 风险控制错误
- **DATABASE_ERROR:** 数据库操作错误
- **NETWORK_ERROR:** 网络连接错误

### 3. 错误恢复机制

- **重试机制:** 指数退避重试策略
- **熔断器:** 防止级联故障
- **降级服务:** 关键功能的备用方案
- **告警通知:** 严重错误的实时通知

## 测试策略

### 1. 单元测试
- **覆盖率目标:** 80%以上
- **测试框架:** Jest + Supertest
- **模拟对象:** 交易所API、AI API的Mock

### 2. 集成测试
- **API接口测试:** 完整的请求-响应流程
- **数据库测试:** SQLite操作的完整性
- **外部服务测试:** 模拟环境下的集成测试

### 3. 端到端测试
- **用户流程测试:** 从配置到交易的完整流程
- **性能测试:** 高频数据处理的性能验证
- **压力测试:** 系统负载能力测试

### 4. 安全测试
- **API密钥安全:** 加密存储和传输测试
- **输入验证:** SQL注入、XSS防护测试
- **权限控制:** 访问控制和身份验证测试

## 安全设计

### 1. API密钥管理
- **加密存储:** 使用AES-256-GCM加密算法存储API密钥
- **环境隔离:** 模拟盘和实盘密钥分离存储
- **访问控制:** 密钥访问需要系统级认证
- **定期轮换:** 支持API密钥的定期更新

### 2. 数据传输安全
- **HTTPS强制:** 所有API通信使用HTTPS
- **请求签名:** 关键操作使用HMAC签名验证
- **速率限制:** API调用频率限制防止滥用
- **CORS配置:** 严格的跨域资源共享策略

### 3. 输入验证和防护
- **参数验证:** 所有输入参数的类型和范围验证
- **SQL注入防护:** 使用参数化查询
- **XSS防护:** 输出内容的HTML转义
- **CSRF防护:** 跨站请求伪造防护令牌

## 性能优化设计

### 1. 数据缓存策略
- **多级缓存:** 内存缓存 + Redis缓存（可选）
- **缓存失效:** 基于时间和事件的缓存失效机制
- **预加载:** 常用数据的预加载策略
- **实时获取:** 所有市场数据通过CCXT实时获取，不进行本地存储

### 2. API优化
- **连接池:** 数据库连接池管理
- **请求合并:** 批量API请求减少网络开销
- **异步处理:** 非阻塞的异步操作
- **负载均衡:** 多实例部署的负载均衡

### 3. 前端性能优化
- **代码分割:** React组件的懒加载
- **虚拟滚动:** 大数据列表的虚拟滚动
- **状态优化:** Redux状态的规范化存储
- **缓存策略:** 静态资源的浏览器缓存

## 监控和日志设计

### 1. 系统监控指标
- **性能指标:** CPU、内存、网络使用率
- **业务指标:** 交易成功率、AI决策准确率
- **错误指标:** 错误率、响应时间
- **资源指标:** 数据库连接数、缓存命中率

### 2. 日志分级和格式
```javascript
// 日志级别
const LOG_LEVELS = {
  ERROR: 0,   // 系统错误
  WARN: 1,    // 警告信息
  INFO: 2,    // 一般信息
  DEBUG: 3,   // 调试信息
  TRACE: 4    // 详细跟踪
};

// 日志格式
{
  timestamp: '2025-01-08T14:40:00.000Z',
  level: 'INFO',
  module: 'TradingService',
  message: '开仓成功',
  data: {
    symbol: 'BTC-USDT-SWAP',
    side: 'long',
    size: 0.1,
    price: 45000
  },
  traceId: 'uuid-trace-id'
}
```

### 3. 告警机制
- **实时告警:** 关键错误的即时通知
- **阈值告警:** 指标超过预设阈值的告警
- **趋势告警:** 基于趋势分析的预警
- **恢复通知:** 故障恢复的确认通知

## 部署和运维设计

### 1. 部署架构
- **单机部署:** 开发和小规模生产环境
- **集群部署:** 高可用生产环境
- **容器化:** Docker容器化部署（可选）
- **自动化:** CI/CD自动化部署流程

### 2. 配置管理
- **环境配置:** 开发、测试、生产环境配置分离
- **动态配置:** 运行时配置的热更新
- **版本控制:** 配置文件的版本管理
- **备份恢复:** 配置和数据的备份恢复策略

### 3. 运维工具
- **健康检查:** 系统健康状态检查接口
- **性能分析:** 性能瓶颈分析工具
- **日志分析:** 日志聚合和分析工具
- **监控面板:** 实时监控仪表板

## 扩展性设计

### 1. 模块化扩展
- **插件架构:** 支持新交易所的插件式接入
- **策略扩展:** 支持自定义交易策略
- **指标扩展:** 支持新技术指标的动态加载
- **AI模型扩展:** 支持多种AI模型的切换

### 2. 数据扩展
- **多交易所:** 支持多个交易所的同时连接
- **多币种:** 支持更多加密货币交易对
- **多时间周期:** 支持更多时间周期的分析
- **实时分析:** 基于实时数据的深度技术分析

### 3. 功能扩展
- **实时策略:** 基于实时数据的策略优化功能
- **组合管理:** 多策略组合的管理
- **风险分析:** 更详细的风险分析报告
- **社交功能:** 策略分享和社区功能
