# 加密货币永续合约全自动量化系统 需求文档

## 功能概述
基于DeepSeek AI模型的加密货币永续合约全自动量化交易系统，通过技术指标分析和AI决策引擎实现智能开仓和持仓管理。系统采用Node.js后端，React+Ant Design前端，支持欧易交易所的模拟盘和实盘交易，使用SQLite3数据库存储配置信息。

## 需求列表

### 1. 交易所集成与数据获取
**用户故事：** 作为量化交易者，我希望系统能够连接欧易交易所并获取实时市场数据，以便进行技术分析和交易决策。

**验收标准：**
1. 当系统启动时，应该能够通过CCXT库连接到欧易交易所的REST API
2. 当需要市场数据时，系统应该能够获取1分钟、5分钟、15分钟、1小时的K线数据
3. 当获取数据时，系统应该包含开高低收价格和成交量信息
4. 当用户切换模拟盘/实盘时，系统应该能够无缝切换API端点
5. 当API调用失败时，系统应该有重试机制和错误处理

### 2. 技术指标计算引擎
**用户故事：** 作为量化交易者，我希望系统能够计算多种技术指标，以便为AI模型提供分析数据。

**验收标准：**
1. 当获取到市场数据时，系统应该使用technicalindicators库计算常用技术指标
2. 当计算指标时，系统应该支持多个时间周期的指标组合分析
3. 当指标计算完成时，系统应该将结果格式化为AI模型可理解的数据结构
4. 当指标数据不足时，系统应该等待足够的历史数据再进行计算
5. 当计算出现错误时，系统应该记录错误并继续处理其他指标

### 3. AI决策引擎集成
**用户故事：** 作为量化交易者，我希望系统能够将技术分析结果发送给DeepSeek AI模型并获得交易建议，以便做出智能交易决策。

**验收标准：**
1. 当技术指标计算完成时，系统应该将数据发送给DeepSeek API
2. 当调用AI模型时，系统应该使用不同的提示词区分开仓引擎和持仓引擎
3. 当AI返回结果时，系统应该解析置信度和交易理由
4. 当置信度达到设定阈值时，系统应该触发相应的交易操作
5. 当AI API调用失败时，系统应该有降级策略和错误处理

### 4. 开仓引擎
**用户故事：** 作为量化交易者，我希望系统能够根据AI分析结果自动开仓，以便抓住交易机会。

**验收标准：**
1. 当AI开仓引擎给出高置信度信号时，系统应该计算合适的仓位大小
2. 当开仓时，系统应该根据设置的最大杠杆和最大仓位限制进行风险控制
3. 当开仓时，系统应该支持多头和空头方向的单向持仓
4. 当开仓时，系统应该设置初始止盈止损价格
5. 当开仓失败时，系统应该记录错误并通知用户

### 5. 持仓引擎
**用户故事：** 作为量化交易者，我希望系统能够智能管理现有持仓，以便优化收益和控制风险。

**验收标准：**
1. 当有持仓时，AI持仓引擎应该定期分析是否需要调整仓位
2. 当持仓引擎建议平仓时，系统应该执行平仓操作
3. 当持仓引擎建议调整止盈止损时，系统应该更新订单
4. 当持仓达到止盈止损条件时，系统应该自动平仓
5. 当持仓管理出现异常时，系统应该有紧急平仓机制

### 6. 风险管理系统
**用户故事：** 作为量化交易者，我希望系统有完善的风险控制机制，以便保护我的资金安全。

**验收标准：**
1. 当计算仓位时，系统应该基于可用资金而非总资产进行计算
2. 当设置杠杆时，系统应该考虑杠杆放大对止盈止损的影响
3. 当总仓位超过设定限制时，系统应该拒绝新的开仓请求
4. 当账户余额不足时，系统应该停止交易并发出警告
5. 当检测到异常交易时，系统应该暂停自动交易

### 7. 前端管理界面
**用户故事：** 作为量化交易者，我希望有一个直观的前端界面来监控和配置交易系统，以便有效管理我的交易策略。

**验收标准：**
1. 当用户访问系统时，应该看到清晰的仪表板显示当前状态
2. 当用户需要配置时，应该能够设置交易参数如最大杠杆、最大仓位、止盈止损
3. 当用户选择交易对时，应该能够从支持的交易对列表中选择
4. 当用户查看交易记录时，应该能够看到历史交易和当前持仓
5. 当用户切换模拟盘/实盘时，应该有明确的界面提示和确认

### 8. 配置和数据管理
**用户故事：** 作为量化交易者，我希望系统能够安全存储我的配置信息，以便系统稳定运行。

**验收标准：**
1. 当用户首次使用时，系统应该引导设置API密钥和交易参数
2. 当存储敏感信息时，系统应该使用SQLite3数据库加密存储
3. 当系统重启时，应该能够从数据库恢复所有配置信息
4. 当用户修改配置时，系统应该实时更新数据库
5. 当数据库操作失败时，系统应该有备份和恢复机制

### 9. 系统监控和日志
**用户故事：** 作为量化交易者，我希望系统能够提供详细的运行日志和监控信息，以便了解系统运行状态和排查问题。

**验收标准：**
1. 当系统运行时，应该记录所有关键操作的日志
2. 当发生错误时，应该记录详细的错误信息和堆栈跟踪
3. 当用户查看日志时，应该能够按时间和类型筛选日志
4. 当系统性能异常时，应该记录性能指标和警告
5. 当日志文件过大时，应该有自动轮转和清理机制

### 10. 全仓保证金交易模式
**用户故事：** 作为量化交易者，我希望系统支持全仓保证金交易模式，以便最大化资金利用效率。

**验收标准：**
1. 当开仓时，系统应该使用全仓模式进行保证金计算
2. 当计算可用保证金时，系统应该考虑所有持仓的总体风险
3. 当保证金不足时，系统应该自动调整仓位或拒绝开仓
4. 当强制平仓风险增加时，系统应该发出警告并考虑减仓
5. 当账户接近爆仓时，系统应该有紧急风控措施
